/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */
@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-animation-delay:0s;--tw-animation-direction:normal;--tw-animation-duration:initial;--tw-animation-fill-mode:none;--tw-animation-iteration-count:1;--tw-enter-opacity:1;--tw-enter-rotate:0;--tw-enter-scale:1;--tw-enter-translate-x:0;--tw-enter-translate-y:0;--tw-exit-opacity:1;--tw-exit-rotate:0;--tw-exit-scale:1;--tw-exit-translate-x:0;--tw-exit-translate-y:0}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-500:oklch(63.7% .237 25.331);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-sm:24rem;--container-lg:32rem;--container-2xl:42rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--leading-tight:1.25;--radius-xs:.125rem;--radius-md:calc(var(--radius) - 2px);--radius-lg:var(--radius);--shadow-sm:0 1px 3px 0 #0000001a,0 1px 2px -1px #0000001a;--shadow-md:0 4px 6px -1px #0000001a,0 2px 4px -2px #0000001a;--shadow-lg:0 10px 15px -3px #0000001a,0 4px 6px -4px #0000001a;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--blur-sm:8px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::-moz-placeholder{opacity:1}::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){::-moz-placeholder{color:currentColor}::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*{border-color:var(--border);outline-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){*{outline-color:color-mix(in oklab,var(--ring)50%,transparent)}}html,body{background-color:var(--background);color:var(--foreground);scrollbar-width:none}:is(html,body)::-webkit-scrollbar{display:none}html,body{font-feature-settings:"kern" 1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif}.text-apple-title1{letter-spacing:-.02em;font-size:2.125rem;font-weight:700;line-height:1.2}.text-apple-title2{letter-spacing:-.01em;font-size:1.75rem;font-weight:700;line-height:1.25}.text-apple-title3{letter-spacing:-.01em;font-size:1.375rem;font-weight:600;line-height:1.3}.text-apple-headline{letter-spacing:-.005em;font-size:1.0625rem;font-weight:600;line-height:1.35}.text-apple-body{letter-spacing:-.005em;font-size:1.0625rem;font-weight:400;line-height:1.35}.text-apple-callout{letter-spacing:-.005em;font-size:1rem;font-weight:400;line-height:1.4}.text-apple-subheadline{letter-spacing:-.005em;font-size:.9375rem;font-weight:400;line-height:1.4}.text-apple-footnote{letter-spacing:-.005em;font-size:.8125rem;font-weight:400;line-height:1.4}.text-apple-caption1{letter-spacing:0;font-size:.75rem;font-weight:400;line-height:1.35}.text-apple-caption2{letter-spacing:.01em;font-size:.6875rem;font-weight:400;line-height:1.35}}@layer components;@layer utilities{.pointer-events-auto{pointer-events:auto}.pointer-events-none{pointer-events:none}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.inset-x-0{inset-inline:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.top-0{top:calc(var(--spacing)*0)}.top-1\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-4{top:calc(var(--spacing)*4)}.top-\[1px\]{top:1px}.top-\[10\%\]{top:10%}.top-\[50\%\]{top:50%}.right-0{right:calc(var(--spacing)*0)}.right-2{right:calc(var(--spacing)*2)}.right-4{right:calc(var(--spacing)*4)}.bottom-0{bottom:calc(var(--spacing)*0)}.left-0{left:calc(var(--spacing)*0)}.left-3{left:calc(var(--spacing)*3)}.left-\[50\%\]{left:50%}.z-10{z-index:10}.z-50{z-index:50}.z-\[1\]{z-index:1}.z-\[10\]{z-index:10}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.mx-auto{margin-inline:auto}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-4{margin-block:calc(var(--spacing)*4)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-5{margin-top:calc(var(--spacing)*5)}.mt-auto{margin-top:auto}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-9{margin-left:calc(var(--spacing)*9)}.ml-auto{margin-left:auto}.no-scrollbar{scrollbar-width:none}.no-scrollbar::-webkit-scrollbar{display:none}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.inline-flex{display:inline-flex}.scrollbar-hidden::-webkit-scrollbar{display:none}.table{display:table}.field-sizing-content{field-sizing:content}.aspect-square{aspect-ratio:1}.size-1{width:calc(var(--spacing)*1);height:calc(var(--spacing)*1)}.size-3\.5{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.size-5{width:calc(var(--spacing)*5);height:calc(var(--spacing)*5)}.size-6{width:calc(var(--spacing)*6);height:calc(var(--spacing)*6)}.size-8{width:calc(var(--spacing)*8);height:calc(var(--spacing)*8)}.size-full{width:100%;height:100%}.h-1\.5{height:calc(var(--spacing)*1.5)}.h-2\.5{height:calc(var(--spacing)*2.5)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-dvh{height:100dvh}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.max-h-\(--radix-select-content-available-height\){max-height:var(--radix-select-content-available-height)}.max-h-screen{max-height:100vh}.min-h-0{min-height:calc(var(--spacing)*0)}.min-h-10{min-height:calc(var(--spacing)*10)}.min-h-16{min-height:calc(var(--spacing)*16)}.min-h-\[60px\]{min-height:60px}.min-h-\[80px\]{min-height:80px}.min-h-\[120px\]{min-height:120px}.w-1\/2{width:50%}.w-2\.5{width:calc(var(--spacing)*2.5)}.w-3\/4{width:75%}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-8{width:calc(var(--spacing)*8)}.w-10{width:calc(var(--spacing)*10)}.w-\[4\.5rem\]{width:4.5rem}.w-\[22\.857rem\]{width:22.857rem}.w-\[45\%\]{width:45%}.w-\[90\%\]{width:90%}.w-auto{width:auto}.w-fit{width:-moz-fit-content;width:fit-content}.w-full{width:100%}.w-px{width:1px}.max-w-2xl{max-width:var(--container-2xl)}.max-w-\[85\%\]{max-width:85%}.max-w-\[calc\(100\%-2rem\)\]{max-width:calc(100% - 2rem)}.max-w-sm{max-width:var(--container-sm)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-\[8rem\]{min-width:8rem}.min-w-\[100px\]{min-width:100px}.min-w-\[180px\]{min-width:180px}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.flex-grow,.grow{flex-grow:1}.border-collapse{border-collapse:collapse}.origin-\(--radix-select-content-transform-origin\){transform-origin:var(--radix-select-content-transform-origin)}.origin-\(--radix-tooltip-content-transform-origin\){transform-origin:var(--radix-tooltip-content-transform-origin)}.translate-x-\[-50\%\]{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\[-50\%\]{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\[calc\(-50\%_-_2px\)\]{--tw-translate-y:calc(-50% - 2px);translate:var(--tw-translate-x)var(--tw-translate-y)}.rotate-0{rotate:none}.rotate-45{rotate:45deg}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-in{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.touch-none{touch-action:none}.resize{resize:both}.resize-none{resize:none}.scroll-my-1{scroll-margin-block:calc(var(--spacing)*1)}.list-decimal{list-style-type:decimal}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.items-center{align-items:center}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-6{gap:calc(var(--spacing)*6)}:where(.space-y-1\.5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1.5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1.5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}:where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.divide-border\/50>:not(:last-child)){border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){:where(.divide-border\/50>:not(:last-child)){border-color:color-mix(in oklab,var(--border)50%,transparent)}}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded-\[2px\]{border-radius:2px}.rounded-\[12px\]{border-radius:12px}.rounded-\[inherit\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-none{border-radius:0}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-xl{border-radius:calc(var(--radius) + 4px)}.rounded-xs{border-radius:var(--radius-xs)}.border{border-style:var(--tw-border-style);border-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-r-0{border-right-style:var(--tw-border-style);border-right-width:0}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-0{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-\[var\(--active\)\],.border-\[var\(--active\)\]\/20{border-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--active\)\]\/20{border-color:color-mix(in oklab,var(--active)20%,transparent)}}.border-\[var\(--active\)\]\/50{border-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--active\)\]\/50{border-color:color-mix(in oklab,var(--active)50%,transparent)}}.border-\[var\(--border\)\]{border-color:var(--border)}.border-\[var\(--text\)\],.border-\[var\(--text\)\]\/10{border-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--text\)\]\/10{border-color:color-mix(in oklab,var(--text)10%,transparent)}}.border-\[var\(--text\)\]\/20{border-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--text\)\]\/20{border-color:color-mix(in oklab,var(--text)20%,transparent)}}.border-\[var\(--text\)\]\/30{border-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--text\)\]\/30{border-color:color-mix(in oklab,var(--text)30%,transparent)}}.border-\[var\(--text\)\]\/50{border-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.border-\[var\(--text\)\]\/50{border-color:color-mix(in oklab,var(--text)50%,transparent)}}.border-border,.border-border\/10{border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.border-border\/10{border-color:color-mix(in oklab,var(--border)10%,transparent)}}.border-border\/30{border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.border-border\/30{border-color:color-mix(in oklab,var(--border)30%,transparent)}}.border-border\/50{border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.border-border\/50{border-color:color-mix(in oklab,var(--border)50%,transparent)}}.border-destructive{border-color:var(--destructive)}.border-foreground{border-color:var(--foreground)}.border-input{border-color:var(--input)}.border-muted-foreground{border-color:var(--muted-foreground)}.border-primary,.border-primary\/20{border-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.border-primary\/20{border-color:color-mix(in oklab,var(--primary)20%,transparent)}}.border-primary\/50{border-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.border-primary\/50{border-color:color-mix(in oklab,var(--primary)50%,transparent)}}.border-transparent{border-color:#0000}.bg-\[rgba\(255\,250\,240\,0\.4\)\]{background-color:#fffaf066}.bg-\[var\(--active\)\],.bg-\[var\(--active\)\]\/50{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.bg-\[var\(--active\)\]\/50{background-color:color-mix(in oklab,var(--active)50%,transparent)}}.bg-\[var\(--bg\)\]{background-color:var(--bg)}.bg-\[var\(--code-inline-bg\)\]{background-color:var(--code-inline-bg)}.bg-\[var\(--input-background\)\]{background-color:var(--input-background)}.bg-\[var\(--muted\)\]{background-color:var(--muted)}.bg-background,.bg-background\/95{background-color:var(--background)}@supports (color:color-mix(in lab, red, red)){.bg-background\/95{background-color:color-mix(in oklab,var(--background)95%,transparent)}}.bg-black\/50{background-color:#00000080}@supports (color:color-mix(in lab, red, red)){.bg-black\/50{background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}.bg-black\/60{background-color:#0009}@supports (color:color-mix(in lab, red, red)){.bg-black\/60{background-color:color-mix(in oklab,var(--color-black)60%,transparent)}}.bg-border{background-color:var(--border)}.bg-card{background-color:var(--card)}.bg-destructive{background-color:var(--destructive)}.bg-muted,.bg-muted\/5{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.bg-muted\/5{background-color:color-mix(in oklab,var(--muted)5%,transparent)}}.bg-popover{background-color:var(--popover)}.bg-primary,.bg-primary\/10{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.bg-primary\/10{background-color:color-mix(in oklab,var(--primary)10%,transparent)}}.bg-primary\/20{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.bg-primary\/20{background-color:color-mix(in oklab,var(--primary)20%,transparent)}}.bg-primary\/50{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.bg-primary\/50{background-color:color-mix(in oklab,var(--primary)50%,transparent)}}.bg-secondary,.bg-secondary\/50{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.bg-secondary\/50{background-color:color-mix(in oklab,var(--secondary)50%,transparent)}}.bg-transparent{background-color:#0000}.fill-current{fill:currentColor}.fill-primary{fill:var(--primary)}.p-0{padding:calc(var(--spacing)*0)}.p-1{padding:calc(var(--spacing)*1)}.p-1\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.px-1{padding-inline:calc(var(--spacing)*1)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-6{padding-inline:calc(var(--spacing)*6)}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-5{padding-block:calc(var(--spacing)*5)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-5{padding-top:calc(var(--spacing)*5)}.pt-6{padding-top:calc(var(--spacing)*6)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-px{padding-right:1px}.pb-1{padding-bottom:calc(var(--spacing)*1)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pb-10{padding-bottom:calc(var(--spacing)*10)}.pb-px{padding-bottom:1px}.pl-2{padding-left:calc(var(--spacing)*2)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-5{padding-left:calc(var(--spacing)*5)}.pl-10{padding-left:calc(var(--spacing)*10)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.font-\[\'Bruno_Ace_SC\'\]{font-family:Bruno Ace SC}.font-\[\'Space_Mono\'\,_monospace\]{font-family:Space Mono,monospace}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.leading-none{--tw-leading:1;line-height:1}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.text-balance{text-wrap:balance}.text-ellipsis{text-overflow:ellipsis}.whitespace-nowrap{white-space:nowrap}.text-\[var\(--active\)\]{color:var(--active)}.text-\[var\(--code-inline-text\)\]{color:var(--code-inline-text)}.text-\[var\(--link\)\]{color:var(--link)}.text-\[var\(--muted-foreground\)\]{color:var(--muted-foreground)}.text-\[var\(--success\)\]{color:var(--success)}.text-\[var\(--text\)\],.text-\[var\(--text\)\]\/60{color:var(--text)}@supports (color:color-mix(in lab, red, red)){.text-\[var\(--text\)\]\/60{color:color-mix(in oklab,var(--text)60%,transparent)}}.text-\[var\(--text\)\]\/70{color:var(--text)}@supports (color:color-mix(in lab, red, red)){.text-\[var\(--text\)\]\/70{color:color-mix(in oklab,var(--text)70%,transparent)}}.text-\[var\(--text\)\]\/80{color:var(--text)}@supports (color:color-mix(in lab, red, red)){.text-\[var\(--text\)\]\/80{color:color-mix(in oklab,var(--text)80%,transparent)}}.text-current{color:currentColor}.text-destructive{color:var(--destructive)}.text-destructive-foreground{color:var(--destructive-foreground)}.text-foreground,.text-foreground\/70{color:var(--foreground)}@supports (color:color-mix(in lab, red, red)){.text-foreground\/70{color:color-mix(in oklab,var(--foreground)70%,transparent)}}.text-foreground\/90{color:var(--foreground)}@supports (color:color-mix(in lab, red, red)){.text-foreground\/90{color:color-mix(in oklab,var(--foreground)90%,transparent)}}.text-muted-foreground,.text-muted-foreground\/80{color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.text-muted-foreground\/80{color:color-mix(in oklab,var(--muted-foreground)80%,transparent)}}.text-popover-foreground{color:var(--popover-foreground)}.text-primary{color:var(--primary)}.text-primary-foreground{color:var(--primary-foreground)}.text-red-500{color:var(--color-red-500)}.text-secondary-foreground{color:var(--secondary-foreground)}.capitalize{text-transform:capitalize}.lowercase{text-transform:lowercase}.italic{font-style:italic}.line-through{text-decoration-line:line-through}.decoration-2{text-decoration-thickness:2px}.underline-offset-4{text-underline-offset:4px}.opacity-0{opacity:0}.opacity-50{opacity:.5}.opacity-70{opacity:.7}.opacity-75{opacity:.75}.opacity-80{opacity:.8}.opacity-90{opacity:.9}.opacity-100{opacity:1}.opacity-\[0\.03\]{opacity:.03}.shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-\[var\(--input-base-shadow\)\]{--tw-shadow:var(--input-base-shadow);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-\[var\(--active\)\]\/50{--tw-ring-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.ring-\[var\(--active\)\]\/50{--tw-ring-color:color-mix(in oklab,var(--active)50%,transparent)}}.ring-offset-\[var\(--bg\)\]{--tw-ring-offset-color:var(--bg)}.ring-offset-background{--tw-ring-offset-color:var(--background)}.outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.outline-hidden{outline-offset:2px;outline:2px solid #0000}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\[color\,box-shadow\,border-color\]{transition-property:color,box-shadow,border-color;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\[color\,box-shadow\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-75{--tw-duration:75ms;transition-duration:75ms}.duration-100{--tw-duration:.1s;transition-duration:.1s}.duration-150{--tw-duration:.15s;transition-duration:.15s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.fade-in-0{--tw-enter-opacity:0}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.zoom-in-95{--tw-enter-scale:.95}.ring-inset{--tw-ring-inset:inset}.running{animation-play-state:running}@media (hover:hover){.group-hover\:rotate-180:is(:where(.group):hover *){rotate:180deg}.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}}.group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled=true] *){pointer-events:none}.group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled=true] *){opacity:.5}.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\:opacity-50:is(:where(.peer):disabled~*){opacity:.5}.selection\:bg-primary ::-moz-selection{background-color:var(--primary)}.selection\:bg-primary ::selection{background-color:var(--primary)}.selection\:bg-primary::-moz-selection{background-color:var(--primary)}.selection\:bg-primary::selection{background-color:var(--primary)}.selection\:text-primary-foreground ::-moz-selection{color:var(--primary-foreground)}.selection\:text-primary-foreground ::selection{color:var(--primary-foreground)}.selection\:text-primary-foreground::-moz-selection{color:var(--primary-foreground)}.selection\:text-primary-foreground::selection{color:var(--primary-foreground)}.file\:inline-flex::file-selector-button{display:inline-flex}.file\:h-7::file-selector-button{height:calc(var(--spacing)*7)}.file\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\:bg-transparent::file-selector-button{background-color:#0000}.file\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\:text-foreground::file-selector-button{color:var(--foreground)}.placeholder\:text-\[var\(--muted-foreground\)\]::-moz-placeholder, .placeholder\:text-muted-foreground::-moz-placeholder{color:var(--muted-foreground)}.placeholder\:text-\[var\(--muted-foreground\)\]::placeholder,.placeholder\:text-muted-foreground::placeholder{color:var(--muted-foreground)}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.even\:bg-\[var\(--muted\)\]\/50:nth-child(2n){background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.even\:bg-\[var\(--muted\)\]\/50:nth-child(2n){background-color:color-mix(in oklab,var(--muted)50%,transparent)}}@media (hover:hover){.hover\:scale-110:hover{--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.hover\:border-\[var\(--active\)\]:hover{border-color:var(--active)}.hover\:border-\[var\(--text\)\]\/70:hover{border-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.hover\:border-\[var\(--text\)\]\/70:hover{border-color:color-mix(in oklab,var(--text)70%,transparent)}}.hover\:border-border\/80:hover{border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.hover\:border-border\/80:hover{border-color:color-mix(in oklab,var(--border)80%,transparent)}}.hover\:border-primary:hover{border-color:var(--primary)}.hover\:bg-\[var\(--active\)\]\/10:hover{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-\[var\(--active\)\]\/10:hover{background-color:color-mix(in oklab,var(--active)10%,transparent)}}.hover\:bg-\[var\(--active\)\]\/20:hover{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-\[var\(--active\)\]\/20:hover{background-color:color-mix(in oklab,var(--active)20%,transparent)}}.hover\:bg-\[var\(--active\)\]\/90:hover{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-\[var\(--active\)\]\/90:hover{background-color:color-mix(in oklab,var(--active)90%,transparent)}}.hover\:bg-\[var\(--error\)\]\/10:hover{background-color:var(--error)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-\[var\(--error\)\]\/10:hover{background-color:color-mix(in oklab,var(--error)10%,transparent)}}.hover\:bg-\[var\(--text\)\]\/10:hover{background-color:var(--text)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-\[var\(--text\)\]\/10:hover{background-color:color-mix(in oklab,var(--text)10%,transparent)}}.hover\:bg-accent:hover{background-color:var(--accent)}.hover\:bg-black\/10:hover{background-color:#0000001a}@supports (color:color-mix(in lab, red, red)){.hover\:bg-black\/10:hover{background-color:color-mix(in oklab,var(--color-black)10%,transparent)}}.hover\:bg-destructive\/10:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-destructive\/10:hover{background-color:color-mix(in oklab,var(--destructive)10%,transparent)}}.hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}.hover\:bg-primary\/10:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-primary\/10:hover{background-color:color-mix(in oklab,var(--primary)10%,transparent)}}.hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}.hover\:bg-secondary:hover,.hover\:bg-secondary\/50:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-secondary\/50:hover{background-color:color-mix(in oklab,var(--secondary)50%,transparent)}}.hover\:bg-secondary\/80:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-secondary\/80:hover{background-color:color-mix(in oklab,var(--secondary)80%,transparent)}}.hover\:bg-transparent:hover{background-color:#0000}.hover\:text-\[var\(--error\)\]:hover{color:var(--error)}.hover\:text-\[var\(--text\)\]:hover{color:var(--text)}.hover\:text-accent-foreground:hover{color:var(--accent-foreground)}.hover\:text-destructive:hover{color:var(--destructive)}.hover\:text-destructive-foreground:hover{color:var(--destructive-foreground)}.hover\:text-foreground:hover{color:var(--foreground)}.hover\:no-underline:hover{text-decoration-line:none}.hover\:underline:hover{text-decoration-line:underline}.hover\:decoration-1:hover{text-decoration-thickness:1px}.hover\:underline-offset-4:hover{text-underline-offset:4px}.hover\:opacity-100:hover{opacity:1}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:brightness-95:hover{--tw-brightness:brightness(95%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.hover\:brightness-98:hover{--tw-brightness:brightness(98%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.hover\:brightness-105:hover{--tw-brightness:brightness(105%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.hover\:brightness-110:hover{--tw-brightness:brightness(110%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}}.focus\:border-none:focus{--tw-border-style:none;border-style:none}.focus\:border-\[var\(--active\)\]:focus{border-color:var(--active)}.focus\:border-primary:focus,.focus\:border-primary\/50:focus{border-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.focus\:border-primary\/50:focus{border-color:color-mix(in oklab,var(--primary)50%,transparent)}}.focus\:bg-\[var\(--active\)\]:focus,.focus\:bg-\[var\(--active\)\]\/20:focus{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.focus\:bg-\[var\(--active\)\]\/20:focus{background-color:color-mix(in oklab,var(--active)20%,transparent)}}.focus\:bg-\[var\(--active\)\]\/30:focus{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.focus\:bg-\[var\(--active\)\]\/30:focus{background-color:color-mix(in oklab,var(--active)30%,transparent)}}.focus\:bg-accent:focus{background-color:var(--accent)}.focus\:bg-transparent:focus{background-color:#0000}.focus\:text-\[var\(--text\)\]:focus{color:var(--text)}.focus\:text-accent-foreground:focus{color:var(--accent-foreground)}.focus\:ring-0:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-1:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-\[var\(--active\)\]:focus{--tw-ring-color:var(--active)}.focus\:ring-primary\/20:focus{--tw-ring-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.focus\:ring-primary\/20:focus{--tw-ring-color:color-mix(in oklab,var(--primary)20%,transparent)}}.focus\:ring-ring:focus{--tw-ring-color:var(--ring)}.focus\:ring-offset-0:focus{--tw-ring-offset-width:0px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.focus-visible\:border-primary:focus-visible{border-color:var(--primary)}.focus-visible\:border-ring:focus-visible{border-color:var(--ring)}.focus-visible\:bg-\[var\(--active\)\]\/20:focus-visible{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:bg-\[var\(--active\)\]\/20:focus-visible{background-color:color-mix(in oklab,var(--active)20%,transparent)}}.focus-visible\:bg-accent:focus-visible{background-color:var(--accent)}.focus-visible\:bg-destructive\/10:focus-visible{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:bg-destructive\/10:focus-visible{background-color:color-mix(in oklab,var(--destructive)10%,transparent)}}.focus-visible\:text-accent-foreground:focus-visible{color:var(--accent-foreground)}.focus-visible\:text-destructive-foreground:focus-visible{color:var(--destructive-foreground)}.focus-visible\:shadow-none:focus-visible{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-0:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-2:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-\[3px\]:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-\[var\(--active\)\]:focus-visible{--tw-ring-color:var(--active)}.focus-visible\:ring-\[var\(--error\)\]:focus-visible{--tw-ring-color:var(--error)}.focus-visible\:ring-\[var\(--ring\)\]:focus-visible{--tw-ring-color:var(--ring)}.focus-visible\:ring-destructive:focus-visible,.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.focus-visible\:ring-ring:focus-visible,.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color:color-mix(in oklab,var(--ring)50%,transparent)}}.focus-visible\:ring-offset-1:focus-visible{--tw-ring-offset-width:1px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\:ring-offset-\[var\(--bg\)\]:focus-visible{--tw-ring-offset-color:var(--bg)}.focus-visible\:outline-none:focus-visible{--tw-outline-style:none;outline-style:none}.active\:bg-\[var\(--active\)\]:active{background-color:var(--active)}.active\:text-muted:active{color:var(--muted)}.active\:brightness-95:active{--tw-brightness:brightness(95%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.has-\[\>svg\]\:px-2:has(>svg){padding-inline:calc(var(--spacing)*2)}.aria-invalid\:border-destructive[aria-invalid=true]{border-color:var(--destructive)}.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[disabled\]\:opacity-50[data-disabled]{opacity:.5}.data-\[highlighted\]\:bg-\[var\(--active\)\]\/15[data-highlighted]{background-color:var(--active)}@supports (color:color-mix(in lab, red, red)){.data-\[highlighted\]\:bg-\[var\(--active\)\]\/15[data-highlighted]{background-color:color-mix(in oklab,var(--active)15%,transparent)}}.data-\[placeholder\]\:text-muted-foreground[data-placeholder]{color:var(--muted-foreground)}.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]{--tw-translate-y:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y:calc(2*var(--spacing)*-1)}.data-\[side\=left\]\:-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x:calc(2*var(--spacing))}.data-\[side\=right\]\:translate-x-1[data-side=right]{--tw-translate-x:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x:calc(2*var(--spacing)*-1)}.data-\[side\=top\]\:-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y:calc(2*var(--spacing))}.data-\[size\=sm\]\:h-8[data-size=sm]{height:calc(var(--spacing)*8)}:is(.\*\:data-\[slot\=select-value\]\:line-clamp-1>*)[data-slot=select-value]{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}:is(.\*\:data-\[slot\=select-value\]\:flex>*)[data-slot=select-value]{display:flex}:is(.\*\:data-\[slot\=select-value\]\:items-center>*)[data-slot=select-value]{align-items:center}:is(.\*\:data-\[slot\=select-value\]\:gap-2>*)[data-slot=select-value]{gap:calc(var(--spacing)*2)}.data-\[state\=checked\]\:bg-primary[data-state=checked]{background-color:var(--primary)}.data-\[state\=checked\]\:text-primary-foreground[data-state=checked]{color:var(--primary-foreground)}.data-\[state\=closed\]\:animate-accordion-up[data-state=closed]{animation:accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out}.data-\[state\=closed\]\:animate-out[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=closed\]\:duration-300[data-state=closed]{--tw-duration:.3s;transition-duration:.3s}.data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity:0}.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale:.95}.data-\[state\=closed\]\:slide-out-to-bottom[data-state=closed]{--tw-exit-translate-y:100%}.data-\[state\=closed\]\:slide-out-to-left[data-state=closed]{--tw-exit-translate-x:-100%}.data-\[state\=closed\]\:slide-out-to-right[data-state=closed]{--tw-exit-translate-x:100%}.data-\[state\=closed\]\:slide-out-to-top[data-state=closed]{--tw-exit-translate-y:-100%}.data-\[state\=open\]\:animate-accordion-down[data-state=open]{animation:accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out}.data-\[state\=open\]\:animate-in[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=open\]\:bg-accent[data-state=open]{background-color:var(--accent)}.data-\[state\=open\]\:bg-secondary[data-state=open]{background-color:var(--secondary)}.data-\[state\=open\]\:text-muted-foreground[data-state=open]{color:var(--muted-foreground)}.data-\[state\=open\]\:duration-500[data-state=open]{--tw-duration:.5s;transition-duration:.5s}.data-\[state\=open\]\:fade-in-0[data-state=open]{--tw-enter-opacity:0}.data-\[state\=open\]\:zoom-in-95[data-state=open]{--tw-enter-scale:.95}.data-\[state\=open\]\:slide-in-from-bottom[data-state=open]{--tw-enter-translate-y:100%}.data-\[state\=open\]\:slide-in-from-left[data-state=open]{--tw-enter-translate-x:-100%}.data-\[state\=open\]\:slide-in-from-right[data-state=open]{--tw-enter-translate-x:100%}.data-\[state\=open\]\:slide-in-from-top[data-state=open]{--tw-enter-translate-y:-100%}@media (min-width:40rem){.sm\:w-\[27\.143rem\]{width:27.143rem}.sm\:max-w-\[425px\]{max-width:425px}.sm\:max-w-lg{max-width:var(--container-lg)}.sm\:max-w-sm{max-width:var(--container-sm)}.sm\:flex-row{flex-direction:row}.sm\:justify-end{justify-content:flex-end}}@media (min-width:48rem){.md\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}}.dark\:border-0:is(.dark *){border-style:var(--tw-border-style);border-width:0}.dark\:bg-\[rgba\(255\,255\,255\,0\.1\)\]:is(.dark *){background-color:#ffffff1a}.dark\:bg-destructive\/60:is(.dark *){background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-destructive\/60:is(.dark *){background-color:color-mix(in oklab,var(--destructive)60%,transparent)}}.dark\:bg-input\/30:is(.dark *){background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-input\/30:is(.dark *){background-color:color-mix(in oklab,var(--input)30%,transparent)}}@media (hover:hover){.dark\:hover\:border-0:is(.dark *):hover{border-style:var(--tw-border-style);border-width:0}.dark\:hover\:bg-white\/10:is(.dark *):hover{background-color:#ffffff1a}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-white\/10:is(.dark *):hover{background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}}.dark\:focus\:border-0:is(.dark *):focus{border-style:var(--tw-border-style);border-width:0}.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.\[\&_\*\]\:text-left *{text-align:left}.\[\&_\*\]\:text-right *{text-align:right}.\[\&_svg\]\:pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:shrink-0 svg{flex-shrink:0}.active\:\[\&_svg\]\:text-muted-foreground:active svg{color:var(--muted-foreground)}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-3\.5 svg:not([class*=size-]){width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-5 svg:not([class*=size-]){width:calc(var(--spacing)*5);height:calc(var(--spacing)*5)}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-7 svg:not([class*=size-]){width:calc(var(--spacing)*7);height:calc(var(--spacing)*7)}.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*=text-]){color:var(--muted-foreground)}.\[\&\:focus\]\:border-none:focus{--tw-border-style:none;border-style:none}.\[\&\:focus\]\:\!border-transparent:focus{border-color:#0000!important}.\[\&\:focus\]\:shadow-none:focus{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.\[\&\:focus\]\:ring-0:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.\[\&\:focus\]\:outline-none:focus{--tw-outline-style:none;outline-style:none}:is(.\*\:\[span\]\:last\:flex>*):is(span):last-child{display:flex}:is(.\*\:\[span\]\:last\:items-center>*):is(span):last-child{align-items:center}:is(.\*\:\[span\]\:last\:gap-2>*):is(span):last-child{gap:calc(var(--spacing)*2)}.\[\&\>button\]\:hidden>button{display:none}.\[\&\>div\]\:\!border-b-0>div{border-bottom-style:var(--tw-border-style)!important;border-bottom-width:0!important}.\[\&\>svg\]\:pointer-events-none>svg{pointer-events:none}.\[\&\>svg\]\:size-3>svg{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{rotate:180deg}@media (hover:hover){a.\[a\&\]\:hover\:bg-accent:hover{background-color:var(--accent)}a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:color-mix(in oklab,var(--secondary)90%,transparent)}}a.\[a\&\]\:hover\:text-accent-foreground:hover{color:var(--accent-foreground)}}}@property --tw-animation-delay{syntax:"*";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:"*";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:"*";inherits:false}@property --tw-animation-fill-mode{syntax:"*";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-opacity{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:"*";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:"*";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:"*";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:"*";inherits:false;initial-value:0}:root{--background:#fff;--foreground:#1d1d1f;--card:#fff;--card-foreground:#1d1d1f;--popover:#fff;--popover-foreground:#1d1d1f;--primary:#007aff;--primary-foreground:#fff;--secondary:#f2f2f7;--secondary-foreground:#1d1d1f;--muted:#f2f2f7;--muted-foreground:#8e8e93;--accent:#007aff;--accent-foreground:#fff;--destructive:#ff3b30;--destructive-foreground:#fff;--border:#d1d1d6;--input:#f2f2f7;--ring:#007aff;--spacing-xs:.25rem;--spacing-sm:.5rem;--spacing-md:.75rem;--spacing-lg:1rem;--spacing-xl:1.5rem;--spacing-2xl:2rem;--spacing-3xl:3rem;--radius-sm:.375rem;--radius:.5rem;--radius-md:.75rem;--radius-lg:1rem;--radius-xl:1.25rem;--shadow-sm:0 1px 2px 0 #0000000d;--shadow:0 1px 3px 0 #0000001a,0 1px 2px 0 #0000000f;--shadow-md:0 4px 6px -1px #0000001a,0 2px 4px -1px #0000000f;--shadow-lg:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;--shadow-xl:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;--chart-1:#007aff;--chart-2:#34c759;--chart-3:#ff9500;--chart-4:#af52de;--chart-5:#ff2d92;--sidebar:#f2f2f7;--sidebar-foreground:#1d1d1f;--sidebar-primary:#007aff;--sidebar-primary-foreground:#fff;--sidebar-accent:#f2f2f7;--sidebar-accent-foreground:#1d1d1f;--sidebar-border:#d1d1d6;--sidebar-ring:#007aff;--input-background:#f2f2f7cc;--markdown-inline-code-foreground:#1d1d1f;--markdown-code-background:#f2f2f7;--markdown-pre-foreground:#fff;--markdown-pre-background:#1d1d1f;--markdown-thead-background:var(--secondary);--markdown-thead-foreground:var(--secondary-foreground);--markdown-link:var(--primary);--markdown-strong:var(--foreground);--markdown-em:var(--foreground);--bg:#fff;--text:#1d1d1f;--active:#007aff}.dark{--background:#000;--foreground:#fff;--card:#1c1c1e;--card-foreground:#fff;--popover:#1c1c1e;--popover-foreground:#fff;--primary:#0a84ff;--primary-foreground:#fff;--secondary:#2c2c2e;--secondary-foreground:#fff;--muted:#2c2c2e;--muted-foreground:#8e8e93;--accent:#0a84ff;--accent-foreground:#fff;--destructive:#ff453a;--destructive-foreground:#fff;--border:#38383a;--input:#2c2c2e;--ring:#0a84ff;--shadow-sm:0 1px 2px 0 #0000004d;--shadow:0 1px 3px 0 #0006,0 1px 2px 0 #0000004d;--shadow-md:0 4px 6px -1px #0006,0 2px 4px -1px #0000004d;--shadow-lg:0 10px 15px -3px #0006,0 4px 6px -2px #0000004d;--shadow-xl:0 20px 25px -5px #0006,0 10px 10px -5px #0003;--chart-1:#0a84ff;--chart-2:#30d158;--chart-3:#ff9f0a;--chart-4:#bf5af2;--chart-5:#ff375f;--sidebar:#1c1c1e;--sidebar-foreground:#fff;--sidebar-primary:#0a84ff;--sidebar-primary-foreground:#fff;--sidebar-accent:#2c2c2e;--sidebar-accent-foreground:#fff;--sidebar-border:#38383a;--sidebar-ring:#0a84ff;--input-background:#2c2c2ecc;--markdown-inline-code-foreground:#fff;--markdown-code-background:#2c2c2e;--markdown-pre-foreground:#000;--markdown-pre-background:#fff;--markdown-thead-background:var(--secondary);--markdown-thead-foreground:var(--secondary-foreground);--markdown-link:var(--primary);--markdown-strong:var(--foreground);--markdown-em:var(--foreground);--bg:#1c1c1e;--text:#fff;--active:#0a84ff}.scrollbar-hide::-webkit-scrollbar{display:none}.scrollbar-hide{-ms-overflow-style:none;scrollbar-width:none}:root{--markdown-h1:var(--foreground);--markdown-h2:var(--foreground);--markdown-h3:var(--foreground);--markdown-table-border:var(--border)}code,pre{font-family:monospace}.markdown-body h1{color:var(--markdown-h1);border-bottom:2px solid var(--markdown-h1);margin:1rem 0;padding-bottom:.5rem;font-size:1.5rem;font-weight:800}.markdown-body h2{color:var(--markdown-h2);border-bottom:1px solid var(--markdown-h2);margin:1rem 0 .75rem;padding-bottom:.4rem;font-size:1.25rem;font-weight:700}.markdown-body h3{color:var(--markdown-h3);border-bottom:1px dashed var(--markdown-h3);margin:.75rem 0 .5rem;padding-bottom:.3rem;font-size:1.1rem;font-weight:600}.markdown-body strong{color:var(--markdown-strong);font-family:Poppins,sans-serif;font-weight:700}.markdown-body em{color:var(--markdown-em);font-style:italic}.markdown-body a{color:var(--markdown-link);border-radius:6px;padding:2px 7px;text-decoration:underline}.markdown-body ul,.markdown-body ol{padding-top:.5rem;padding-bottom:.5rem;padding-left:2rem}.markdown-body ul{list-style-type:disc}.markdown-body ol{list-style-type:decimal}.markdown-body p{word-break:break-word;overflow-wrap:break-word;white-space:pre-wrap;padding-top:0;padding-bottom:.2rem}.markdown-body pre{background:var(--markdown-pre-background);color:var(--markdown-pre-foreground);white-space:pre-wrap;max-width:100%;word-wrap:break-all;text-indent:0;border-radius:4px;margin:0;padding:1rem;font-family:monospace;display:block;overflow-x:auto}pre>code{text-indent:0;color:inherit;white-space:inherit;display:block}.markdown-body pre::-webkit-scrollbar{width:8px}.markdown-body pre::-webkit-scrollbar-thumb{background-color:#80808080;border-radius:4px}.markdown-body pre::-webkit-scrollbar-track{background-color:#0000}.markdown-body pre{scrollbar-width:thin;scrollbar-color:#80808080 transparent}.markdown-body code{color:var(--markdown-inline-code-foreground);background:var(--markdown-code-background);word-wrap:break-word;text-indent:0;border-radius:4px;padding:.2rem .4rem;font-family:monospace}.markdown-table-wrapper{scrollbar-width:thin;scrollbar-color:#80808080 transparent;max-width:100%;margin:1rem 0;overflow-x:auto}.markdown-table-wrapper::-webkit-scrollbar{width:8px;height:8px}.markdown-table-wrapper::-webkit-scrollbar-thumb{background-color:#80808080;border-radius:4px}.markdown-table-wrapper::-webkit-scrollbar-track{background-color:#0000}.markdown-body table{border:2px solid var(--markdown-table-border);border-collapse:collapse;width:100%}.markdown-body thead{background:var(--markdown-thead-background);border-bottom:2px solid var(--markdown-table-border);color:var(--markdown-thead-foreground)}.markdown-body th,.markdown-body td{border:1px solid var(--markdown-table-border);padding:.5rem}.markdown-body tr:hover{background:#0000000d}.markdown-body blockquote{border-left:4px solid var(--markdown-h2);color:var(--markdown-h2);background:#00000008;margin:1em 0;padding:.5em 1em}.markdown-body hr{border:none;border-top:1px solid var(--markdown-h2);margin:1.5em 0}.markdown-body sub,.markdown-body sup{vertical-align:baseline;font-size:.8em;line-height:0;position:relative}.markdown-body sup{top:-.5em}.markdown-body sub{bottom:-.2em}@media (max-width:600px){.markdown-body pre{max-width:98vw;font-size:.95em}.markdown-body table{font-size:.95em}}.chatMessage{transition:background-color .15s ease-in-out}.chatMessage:hover{background-color:var(--muted);border-radius:var(--radius-md)}.message-markdown{line-height:1.6}.message-markdown p:first-child{margin-top:0}.message-markdown p:last-child{margin-bottom:0}.focus-visible\:ring-1.focus-visible\:ring-offset-0.focus-visible.AutoResizeTextarea{box-shadow:inset 0 0 0 1px #0000001a}.fingerprint-pulse-btn{border:0px solid var(--active);background:var(--bg,#fff);box-shadow:0 0 0 0 var(--active);border-radius:50%;padding:.5rem;transition:border-color .5s;animation:2s cubic-bezier(.66,0,0,1) infinite fingerprint-shadow-pulse;position:relative;overflow:visible}@keyframes fingerprint-shadow-pulse{0%{box-shadow:0 0 0 0 var(--active)}70%{box-shadow:0 0 0 12px #66d7ee00}to{box-shadow:0 0 #66d7ee00}}:root{--input-base-shadow:0 4px 24px 0 #0000001f;--input-anim-static-shadow:0 2px 0 #0000;--input-pulse-color:var(--active,var(--primary))}@keyframes input-breathing{0%{box-shadow:0 0 0 1px var(--input-pulse-color),var(--input-anim-static-shadow),var(--input-base-shadow)}25%{box-shadow:0 0 6px 2px var(--input-pulse-color),var(--input-anim-static-shadow),var(--input-base-shadow)}50%{box-shadow:0 0 12px 3px var(--input-pulse-color),var(--input-anim-static-shadow),var(--input-base-shadow)}75%{box-shadow:0 0 6px 2px var(--input-pulse-color),var(--input-anim-static-shadow),var(--input-base-shadow)}to{box-shadow:0 0 0 1px var(--input-pulse-color),var(--input-anim-static-shadow),var(--input-base-shadow)}}.dark .input-breathing:focus,.dark .input-breathing.focus-visible{box-shadow:var(--input-anim-static-shadow),var(--input-base-shadow);outline:none}.dark .input-breathing{transition:all ease-in-out;animation:9s cubic-bezier(.4,0,.6,1) infinite input-breathing}.hide-number-spinners::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.hide-number-spinners::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.hide-number-spinners{-moz-appearance:textfield}.chromepanion-title-blade-glow{z-index:0;background:var(--active);border-radius:.75em;position:relative;overflow:hidden}.chromepanion-title-blade-glow:before{content:"";border-radius:inherit;pointer-events:none;z-index:1;-webkit-mask-composite:xor;box-sizing:border-box;-webkit-mask-composite:xor;-webkit-mask-source-type:auto,auto;background-image:conic-gradient(#ffffff1a 0%,#ffffff4d 25%,#ffffff1a 50%);background-size:200% 200%;padding:1px;animation:5s linear infinite lightCircleMove;position:absolute;inset:0;-webkit-mask-image:linear-gradient(#fff 0 0),linear-gradient(#fff 0 0);-webkit-mask-position:0 0,0 0;-webkit-mask-size:auto,auto;-webkit-mask-repeat:repeat,repeat;-webkit-mask-clip:content-box,border-box;-webkit-mask-origin:content-box,border-box;-webkit-mask-composite:xor;mask-composite:exclude;-webkit-mask-source-type:auto,auto;mask-mode:match-source,match-source}@keyframes lightCircleMove{0%{background-position:0 0}to{background-position:100% 100%}}.ball{opacity:.7;border-radius:100%;position:absolute}@keyframes shake{0%,to{transform:rotate(2deg)}25%{transform:rotate(1deg)}50%{transform:rotate(-1deg)}75%{transform:rotate(1deg)}}#kofi-widget{border-radius:8px;transition:all .3s cubic-bezier(.4,0,.2,1);animation:.5s ease-in-out 6 shake;display:inline-block}#kofi-widget:hover{filter:brightness(1.1);transform:scale(1.05)}#kofi-widget img{border-radius:6px;transition:all .3s cubic-bezier(.4,0,.2,1);box-shadow:0 2px 8px #0000001a}#kofi-widget:hover img{box-shadow:0 4px 16px #00000026}@keyframes cardHover{0%{transform:translateY(0);box-shadow:0 1px 3px #0000001a,0 1px 2px #0000000f}to{transform:translateY(-2px);box-shadow:0 10px 25px -3px #0000001a,0 4px 6px -2px #0000000d}}.settings-card{transition:all .3s cubic-bezier(.4,0,.2,1)}.settings-card:hover{animation:.3s cubic-bezier(.4,0,.2,1) forwards cardHover}.settings-card-header{background:linear-gradient(135deg,var(--muted)0%,var(--muted)/80 100%);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.settings-card:focus-within{box-shadow:0 0 0 1px var(--border),0 4px 12px -2px #00000014;outline:none}.settings-card input:focus,.settings-card textarea:focus,.settings-card select:focus,.settings-card [role=slider]:focus,.settings-card [role=combobox]:focus{border-color:var(--muted-foreground)!important;box-shadow:0 0 0 1px var(--muted-foreground),0 2px 4px -1px #0000000f!important;ring:none!important;outline:none!important}.settings-card button:focus,.settings-card button:focus-visible{box-shadow:0 0 0 1px var(--muted-foreground),0 2px 4px -1px #0000000f!important;ring:none!important;outline:none!important}.settings-card input[type=radio]:focus,.settings-card input[type=checkbox]:focus{box-shadow:0 0 0 1px var(--muted-foreground)!important;ring:none!important;outline:none!important}.settings-card input,.settings-card textarea,.settings-card button,.settings-card select,.settings-card [role=slider],.settings-card [role=combobox]{transition:all .2s ease-in-out}:focus,:focus-visible{box-shadow:0 0 0 1px var(--muted-foreground)!important;outline:none!important}input:focus,textarea:focus,select:focus,button:focus,[role=slider]:focus,[role=combobox]:focus,[role=button]:focus,[data-radix-collection-item]:focus{border-color:var(--muted-foreground)!important;box-shadow:0 0 0 1px var(--muted-foreground),0 2px 4px -1px #0000000f!important;--ring:none!important;--ring-color:transparent!important;--ring-offset-color:transparent!important;outline:none!important}#user-input:focus{box-shadow:none!important;--ring:none!important;--ring-color:transparent!important;--ring-offset-color:transparent!important;border:#0000!important;outline:none!important}[data-state=open]:focus,[data-state=checked]:focus,[data-highlighted]:focus{box-shadow:0 0 0 1px var(--muted-foreground)!important;outline:none!important}input[type=range]:focus{box-shadow:none!important;outline:none!important}input[type=range]:focus::-webkit-slider-thumb{box-shadow:0 0 0 1px var(--muted-foreground)!important}input[type=range]:focus::-moz-range-thumb{box-shadow:0 0 0 1px var(--muted-foreground)!important}.focus\:ring-2:focus,.focus\:ring-primary:focus,.focus\:ring-blue-500:focus,.focus\:border-primary:focus,.focus\:border-blue-500:focus{--ring-color:var(--muted-foreground)!important;--ring-opacity:.3!important;border-color:var(--muted-foreground)!important;box-shadow:0 0 0 1px var(--muted-foreground)!important}.settings-card :focus,.settings-card :focus-visible{--ring:none!important;--ring-color:transparent!important;--ring-offset-color:transparent!important;outline:none!important}.settings-card input:hover,.settings-card textarea:hover,.settings-card select:hover,.settings-card button:hover:not(:disabled){border-color:var(--border)!important;box-shadow:0 1px 3px -1px #00000014!important}@keyframes cityLights{0%{color:#b8bee0;text-shadow:0 0 1em #f0a3,0 0 .125em #ff33bb4d,-1em -.125em .5em #fb30,1em .125em .5em #3bf0}30%{color:#d1d8fa;text-shadow:0 0 1em #ff00aa80,0 0 .125em #ff33bb80,-.5em -.125em .25em #fb33,.5em .125em .25em #3bf6}40%{color:#e6eaff;text-shadow:0 0 1em #ff00aa80,0 0 .125em #ffccee80,-.25em -.125em .125em #fb33,.25em .125em .125em #3bf6}70%{color:#d1d8fa;text-shadow:0 0 1em #ff00aa80,0 0 .125em #ff33bb80,.5em -.125em .25em #fb33,-.5em .125em .25em #3bf6}to{color:#b8bee0;text-shadow:0 0 1em #f0a3,0 0 .125em #ff33bb4d,1em -.125em .5em #fb30,-1em .125em .5em #3bf0}}.header-title-glow{font-weight:300;transition:all .3s}.dark .header-title-glow{animation:5s linear .75s infinite cityLights}:not(.dark) .header-title-glow{color:var(--text);text-shadow:none}.configuration-title,.chat-history-title{font-size:1.5rem;font-weight:300}.thin-scrollbar::-webkit-scrollbar{width:8px}.thin-scrollbar::-webkit-scrollbar-thumb{background-color:#80808080;border-radius:4px}.thin-scrollbar::-webkit-scrollbar-track{background-color:#0000}.thin-scrollbar{scrollbar-width:thin;scrollbar-color:#80808080 transparent}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@keyframes spin{to{transform:rotate(360deg)}}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}to{height:0}}

